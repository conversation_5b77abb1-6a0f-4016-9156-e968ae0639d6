import { useEffect, useMemo } from 'react'
import { useChat } from '../contexts/ChatContext'
import { useToast } from '../components/ui/Toaster'
import { Mic, PhoneOff } from 'lucide-react'
import ProfileDropdown from '../components/ui/ProfileDropdown'
import { Orb } from '../components/ui/shadcn-io/orb'

import { blendEmotionHues, getEmotionIntensity, getDominantEmotion } from '../utils/emotionToHue'
import { generateGridOverlay } from '../utils/emotionColorMapping'
import {
  VoiceInterfaceErrorBoundary,
  usePerformanceMonitor,
  accessibilityUtils
} from '../utils/performanceOptimization'

export default function Chat2Page() {
  const {
    isConnected,
    isConnecting,
    connectionError,
    isChatActive,
    messages,
    isRecording,
    isPlaying,
    connect,
    disconnect: _disconnect,
    startChat,
    endChat
  } = useChat()

  const { addToast } = useToast()

  // Performance and accessibility monitoring
  const performanceMetrics = usePerformanceMonitor()
  // const prefersReducedMotion = useReducedMotion()

  // Get latest emotions from messages for real-time visual feedback
  const latestEmotions = useMemo(() => {
    if (messages.length === 0) {
      console.log('🔍 No messages yet, using default emotions')
      return {}
    }

    // Get the most recent user message with emotions (user messages have emotion data)
    const userMessages = messages.filter(m => m.role === 'user' && m.emotions && Object.keys(m.emotions).length > 0)
    const latestUserMessage = userMessages[userMessages.length - 1]

    if (latestUserMessage) {
      console.log('🔍 Using emotions from latest user message:', {
        content: latestUserMessage.content?.substring(0, 50),
        emotions: latestUserMessage.emotions,
        emotionCount: Object.keys(latestUserMessage.emotions).length
      })
      return latestUserMessage.emotions
    }

    console.log('🔍 No user messages with emotions found')
    return {}
  }, [messages])

  // Calculate dynamic hue and intensity based on emotions for the orb background
  const orbHue = useMemo(() => {
    const hue = blendEmotionHues(latestEmotions)
    console.log('🎨 Orb hue calculated:', { latestEmotions, hue })
    return hue
  }, [latestEmotions])

  const orbIntensity = useMemo(() => {
    const intensity = getEmotionIntensity(latestEmotions)
    console.log('💫 Orb intensity calculated:', { latestEmotions, intensity })
    return intensity
  }, [latestEmotions])

  const dominantEmotion = useMemo(() => {
    const emotion = getDominantEmotion(latestEmotions)
    console.log('🎭 Dominant emotion calculated:', { latestEmotions, emotion })
    return emotion
  }, [latestEmotions])

  // Auto-connect when component mounts
  useEffect(() => {
    if (!isConnected && !isConnecting) {
      connect()
    }
  }, [isConnected, isConnecting, connect])

  // Handle connection errors
  useEffect(() => {
    if (connectionError) {
      addToast({
        type: 'error',
        title: 'Connection Error',
        message: connectionError
      })
    }
  }, [connectionError, addToast])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.code === 'Space' || e.code === 'Enter') {
        e.preventDefault()
        if (!isChatActive && isConnected) {
          handleStartChat()
        } else if (isChatActive) {
          handleEndChat()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [isChatActive, isConnected])

  const handleConnect = async () => {
    try {
      await connect()
    } catch (error) {
      addToast({
        type: 'error',
        title: 'Connection Failed',
        message: 'Failed to connect to chat server'
      })
    }
  }

  const handleStartChat = async () => {
    try {
      // Start chat (this will handle microphone access and audio setup)
      await startChat()
      accessibilityUtils.announceVoiceState('listening')
    } catch (error) {
      console.error('Failed to start chat:', error)
      addToast({
        type: 'error',
        title: 'Microphone Access Required',
        message: 'Please allow microphone access to start voice chat'
      })
      accessibilityUtils.announceToScreenReader('Failed to start voice chat. Please check microphone permissions.')
    }
  }

  const handleEndChat = () => {
    endChat()
    accessibilityUtils.announceVoiceState('idle')
  }

  return (
    <VoiceInterfaceErrorBoundary>
      <div
        className="h-screen w-full relative overflow-hidden bg-black"
        style={{
          backgroundImage: generateGridOverlay(),
          backgroundSize: '40px 40px'
        }}
        role="main"
        aria-label="Voice chat interface with orb background"
      >
        {/* Shadcn.io Orb Background - Positioned above the black grid background */}
        <div className="absolute inset-0 z-10 flex items-center justify-center">
          <div className="w-96 h-96">
            <Orb
              hue={orbHue}
              hoverIntensity={orbIntensity * 0.5}
              rotateOnHover={true}
              forceHoverState={isChatActive}
            />
          </div>

          {/* Debug info overlay */}
          <div className="absolute top-4 left-4 bg-black/70 text-white p-2 rounded text-xs z-50 border border-white/20">
            <div>Hue: {orbHue}°</div>
            <div>Intensity: {orbIntensity.toFixed(2)}</div>
            <div>Dominant: {dominantEmotion}</div>
            <div>Emotions: {Object.keys(latestEmotions).length}</div>
          </div>
        </div>

        {/* Subtle vignette effect for depth */}
        <div className="absolute inset-0 bg-gradient-radial from-transparent via-transparent to-black/30 pointer-events-none z-5" />

        {/* Profile Dropdown - Top Right */}
        <div className="absolute top-6 right-6 z-30">
          <ProfileDropdown />
        </div>

        {/* Main Content */}
        <div className="relative z-20 h-full flex flex-col items-center justify-center">
          {/* Emotion and State Display */}
          <div className="mb-12 text-center">
            <div className="text-white/80 text-sm mb-2">
              Current emotion: <span className="text-white font-medium">{dominantEmotion}</span>
            </div>
            <div className="text-white/60 text-xs">
              Intensity: {Math.round(orbIntensity * 100)}%
            </div>
          </div>

          {/* Voice State Indicator */}
          <div className="mb-8 text-center">
            {!isConnected ? (
              <div className="text-white/90">
                <div className="animate-pulse mb-2 text-lg">Connecting to ORA...</div>
              </div>
            ) : !isChatActive ? (
              <div className="text-white">
                <div className="text-xl font-medium mb-3">Ready to chat with ORA</div>
                <div className="text-base text-white/80">Tap the orb to begin your voice conversation</div>
              </div>
            ) : isRecording ? (
              <div className="text-blue-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-blue-400 rounded-full animate-pulse shadow-lg shadow-blue-400/50"></div>
                  <span className="text-xl font-medium">Listening...</span>
                </div>
                <div className="text-base text-blue-200">Speak now, ORA is listening</div>
              </div>
            ) : isPlaying ? (
              <div className="text-green-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50"></div>
                  <span className="text-xl font-medium">ORA is speaking...</span>
                </div>
                <div className="text-base text-green-200">Listen to ORA's response</div>
              </div>
            ) : (
              <div className="text-yellow-300">
                <div className="flex items-center justify-center space-x-3 mb-3">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full animate-pulse shadow-lg shadow-yellow-400/50"></div>
                  <span className="text-xl font-medium">Processing...</span>
                </div>
                <div className="text-base text-yellow-200">ORA is thinking about your message</div>
              </div>
            )}
          </div>

          {/* Voice Controls */}
          <div className="flex items-center justify-center space-x-6">
            {!isChatActive ? (
              <button
                onClick={handleStartChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleStartChat)}
                disabled={!isConnected}
                aria-label="Start voice conversation with ORA"
                aria-describedby="voice-status"
                className={`
                  w-20 h-20 rounded-full flex items-center justify-center
                  transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-white/20
                  ${isConnected
                    ? 'bg-white/10 backdrop-blur-sm text-white shadow-lg shadow-white/10 hover:shadow-xl hover:shadow-white/20 border border-white/20'
                    : 'bg-gray-900/50 text-gray-500 cursor-not-allowed border border-gray-700'
                  }
                `}
              >
                <Mic className="w-8 h-8" />
              </button>
            ) : (
              <button
                onClick={handleEndChat}
                onKeyDown={(e) => accessibilityUtils.handleKeyboardNavigation(e, handleEndChat)}
                aria-label="End voice conversation"
                className="
                  w-20 h-20 rounded-full flex items-center justify-center
                  bg-red-500/80 backdrop-blur-sm text-white shadow-lg shadow-red-500/30
                  transition-all duration-300 transform hover:scale-105 hover:bg-red-600/80
                  focus:outline-none focus:ring-4 focus:ring-red-500/20 border border-red-400/30
                "
              >
                <PhoneOff className="w-8 h-8" />
              </button>
            )}
          </div>

          {/* Connection Status */}
          {!isConnected && (
            <div className="mt-8 text-center">
              <button
                onClick={handleConnect}
                className="px-6 py-3 bg-white/10 backdrop-blur-sm text-white rounded-lg border border-white/20 hover:bg-white/20 transition-all duration-200"
              >
                Reconnect
              </button>
            </div>
          )}

          {/* Performance indicator for development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="absolute top-8 left-8 z-30">
              <div className={`
                bg-black/80 backdrop-blur-sm rounded-full px-3 py-1 text-xs border border-white/20
                ${performanceMetrics.isOptimal ? 'text-green-400' : 'text-red-400'}
              `}>
                {performanceMetrics.fps} FPS
              </div>
            </div>
          )}

          {/* Screen reader status updates */}
          <div id="voice-status" className="sr-only" aria-live="polite" aria-atomic="true">
            {!isConnected ? 'Connecting to ORA...' :
             !isChatActive ? 'Voice chat ready. Press Enter or Space to start.' :
             isRecording ? 'ORA is listening. Speak now.' :
             isPlaying ? 'ORA is responding.' :
             'ORA is processing your message.'}
          </div>
        </div>
      </div>
    </VoiceInterfaceErrorBoundary>
  )
}
